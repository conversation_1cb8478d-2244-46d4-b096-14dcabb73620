import { Socket } from 'socket.io';
import { GameService } from '../services/gameService.js';
import { sessionService } from '../services/sessionService.js';
import { GameState, GameType, EndReason } from '../types/game.js';
import { getAuthenticatedUser, AuthenticatedSocket } from '../middleware/auth.js';
import { logger } from '../utils/logger.js';

/**
 * Example Party Game Controller showing how to integrate room-level and user-level game states
 * This demonstrates the refactored session service logic where:
 * - Room sessions contain multiple user sessions
 * - Each user session tracks individual game state
 * - Room-level game state manages shared game data
 */
export class PartyGameController {
  private gameService: GameService;

  constructor(gameService: GameService) {
    this.gameService = gameService;
  }

  /**
   * Initialize a party game session
   * Creates room-level game state and initializes user sessions
   */
  public initializePartyGame(roomId: string, gameType: GameType): boolean {
    try {
      // Create room-level game state for shared data
      const roomGameState = this.gameService.createGameState(roomId, gameType, 3);
      
      // Get all users in the room
      const roomUsers = sessionService.getRoomUsers(roomId);
      
      if (roomUsers.length === 0) {
        logger.warn(`No users found in room ${roomId} for party game initialization`);
        return false;
      }

      // Initialize each user's game state
      for (const userSession of roomUsers) {
        sessionService.updateUserGameState(userSession.user.userId, {
          gameType: gameType,
          status: 'waiting',
          score: 0,
          scoreAry: [],
          lives: 3
        });
      }

      logger.info(`Party game initialized for room ${roomId} with ${roomUsers.length} players`);
      return true;
    } catch (error) {
      logger.error(`Failed to initialize party game for room ${roomId}:`, error);
      return false;
    }
  }

  /**
   * Start the party game for all users in the room
   */
  public startPartyGame(roomId: string): boolean {
    try {
      // Start room-level game
      const roomGameState = this.gameService.getGameState(roomId);
      if (!roomGameState) {
        logger.error(`No room game state found for ${roomId}`);
        return false;
      }

      // Start the room-level game
      const roomUsers = sessionService.getRoomUsers(roomId);
      if (roomUsers.length === 0) {
        logger.error(`No users in room ${roomId} to start party game`);
        return false;
      }

      // Use a socket from one of the users to start the game
      const firstUserSocket = roomUsers[0].socketId;
      // Note: In a real implementation, you'd need to get the actual socket instance
      // this.gameService.startGame(roomId, socket);

      // Update all user game states to active
      sessionService.updateRoomUsersGameState(roomId, { status: 'active' });

      // Sync user states with room state
      sessionService.syncUserGameStatesWithRoom(roomId, roomGameState, ['gameType', 'status']);

      logger.info(`Party game started for room ${roomId} with ${roomUsers.length} players`);
      return true;
    } catch (error) {
      logger.error(`Failed to start party game for room ${roomId}:`, error);
      return false;
    }
  }

  /**
   * Handle a user action in the party game
   */
  public handleUserAction(userId: string, actionData: any): boolean {
    try {
      const userSession = sessionService.getUserSession(userId);
      if (!userSession) {
        logger.error(`User session not found for ${userId}`);
        return false;
      }

      const roomId = userSession.user.roomId;
      const roomGameState = this.gameService.getGameState(roomId);
      
      if (!roomGameState || roomGameState.status !== 'active') {
        logger.warn(`Game not active in room ${roomId} for user ${userId}`);
        return false;
      }

      // Process the action and update user's individual score
      const points = this.calculatePoints(actionData);
      const currentGameState = sessionService.getUserGameState(userId);
      
      if (currentGameState) {
        const newScore = currentGameState.score + points;
        sessionService.updateUserGameState(userId, {
          score: newScore,
          scoreAry: [...currentGameState.scoreAry, points]
        });

        logger.info(`User ${userId} scored ${points} points, total: ${newScore}`);
      }

      return true;
    } catch (error) {
      logger.error(`Failed to handle user action for ${userId}:`, error);
      return false;
    }
  }

  /**
   * End the party game and get final results
   */
  public async endPartyGame(roomId: string, reason: EndReason = 'manual'): Promise<boolean> {
    try {
      // End room-level game
      this.gameService.endGame(roomId, reason);

      // Update all user states to ended
      sessionService.updateRoomUsersGameState(roomId, { status: 'ended' });

      // Get final leaderboard
      const leaderboard = sessionService.getRoomLeaderboard(roomId);
      
      logger.info(`Party game ended in room ${roomId}. Final leaderboard:`, leaderboard);

      // Submit scores for all users
      await this.submitAllUserScores(roomId, reason);

      return true;
    } catch (error) {
      logger.error(`Failed to end party game for room ${roomId}:`, error);
      return false;
    }
  }

  /**
   * Get current party game status
   */
  public getPartyGameStatus(roomId: string) {
    const roomGameState = this.gameService.getGameState(roomId);
    const userGameStates = sessionService.getRoomUserGameStates(roomId);
    const leaderboard = sessionService.getRoomLeaderboard(roomId);
    const activePlayerCount = sessionService.getActivePlayerCount(roomId);
    const allFinished = sessionService.areAllUsersFinished(roomId);

    return {
      roomGameState,
      userGameStates,
      leaderboard,
      activePlayerCount,
      allFinished,
      totalPlayers: userGameStates.length
    };
  }

  /**
   * Setup socket handlers for party game events
   */
  public setupSocketHandlers(socket: AuthenticatedSocket): void {
    socket.on('party_game_action', (data) => {
      const user = getAuthenticatedUser(socket);
      if (user) {
        sessionService.updateUserActivity(user.userId);
        this.handleUserAction(user.userId, data);
      }
    });

    socket.on('get_party_status', () => {
      const user = getAuthenticatedUser(socket);
      if (user) {
        const status = this.getPartyGameStatus(user.roomId);
        socket.emit('party_status', status);
      }
    });

    socket.on('get_leaderboard', () => {
      const user = getAuthenticatedUser(socket);
      if (user) {
        const leaderboard = sessionService.getRoomLeaderboard(user.roomId);
        socket.emit('leaderboard_update', leaderboard);
      }
    });
  }

  /**
   * Calculate points based on action data (example implementation)
   */
  private calculatePoints(actionData: any): number {
    // Example scoring logic
    if (actionData.correct) {
      return actionData.reactionTime ? Math.max(10, 100 - actionData.reactionTime) : 50;
    }
    return -10; // Penalty for wrong action
  }

  /**
   * Submit scores for all users in the room
   */
  private async submitAllUserScores(roomId: string, reason: EndReason): Promise<void> {
    const userGameStates = sessionService.getRoomUserGameStates(roomId);
    
    for (const userState of userGameStates) {
      try {
        const sessionData = sessionService.getUserSessionData(userState.userId);
        if (sessionData) {
          // Submit individual user score
          // await submitGameScore({
          //   roomId,
          //   score: userState.gameState.score,
          //   scoreArray: userState.gameState.scoreAry,
          //   submitScoreId: sessionData.submitScoreId,
          //   authToken: sessionData.authToken
          // });
          
          logger.info(`Score submitted for user ${userState.userId}: ${userState.gameState.score}`);
        }
      } catch (error) {
        logger.error(`Failed to submit score for user ${userState.userId}:`, error);
      }
    }
  }
}
