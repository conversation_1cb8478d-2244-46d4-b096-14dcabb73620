import { Server, Socket } from 'socket.io';
import { FingerFrenzy<PERSON><PERSON>roller, BingoController, MatchingMayhemController, NumberSequenceController } from '../controllers';
import gameService from '../services/gameService';
import { authMiddleware, AuthenticatedSocket, getAuthenticatedUser, validateRoomAccess } from '../middleware/auth.js';
import { sessionService } from '../services/sessionService.js';
import { logger } from '../utils/logger.js';

// Initialize game controllers
const fingerFrenzyController = new FingerFrenzyController(gameService);
const bingoController = new BingoController(gameService);
const matchingMayhemController = new MatchingMayhemController(gameService);
const numberSequenceController = new NumberSequenceController(gameService);

export function setupSocketHandlers(io: Server) {
  // Apply authentication middleware
  io.use(authMiddleware);

  io.on('connection', (socket: Socket) => {
    // After authentication middleware, socket has user property
    const authenticatedSocket = socket as AuthenticatedSocket;
    const user = getAuthenticatedUser(authenticatedSocket);
    if (!user) {
      logger.error('Authenticated socket missing user data', { socketId: authenticatedSocket.id });
      authenticatedSocket.disconnect();
      return;
    }

    logger.info(`Authenticated client connected`, {
      socketId: authenticatedSocket.id,
      userId: user.userId,
      gameId: user.gameId,
      roomId: user.roomId
    });

    // Create user session
    sessionService.createUserSession(user, authenticatedSocket.id);

    // Automatically join the user's designated room
    authenticatedSocket.join(user.roomId);

    // Setup game-specific handlers
    fingerFrenzyController.setupSocketHandlers(authenticatedSocket);
    bingoController.setupSocketHandlers(io, authenticatedSocket);
    matchingMayhemController.setupSocketHandlers(io, authenticatedSocket);
    numberSequenceController.setupSocketHandlers(io, authenticatedSocket);

    // Handle room joining (with authentication validation)
    authenticatedSocket.on('join_room', (data) => {
      const { roomId } = data;

      // Validate room access
      if (!validateRoomAccess(user, roomId)) {
        authenticatedSocket.emit('error', {
          message: 'Access denied to room',
          code: 'ROOM_ACCESS_DENIED'
        });
        return;
      }

      // Update user activity
      sessionService.updateUserActivity(user.userId);

      logger.info(`User joining room`, {
        userId: user.userId,
        roomId,
        gameId: user.gameId
      });

      authenticatedSocket.join(roomId);
      authenticatedSocket.emit('room_joined', {
        roomId,
        userId: user.userId,
        gameId: user.gameId,
        message: 'Successfully joined room'
      });

      // Notify other players in the room
      authenticatedSocket.to(roomId).emit('player_joined', {
        userId: user.userId,
        gameId: user.gameId
      });
    });

    // Handle room leaving
    authenticatedSocket.on('leave_room', (data) => {
      const { roomId } = data;

      // Validate room access
      if (!validateRoomAccess(user, roomId)) {
        return;
      }

      logger.info(`User leaving room`, {
        userId: user.userId,
        roomId
      });

      authenticatedSocket.leave(roomId);
      authenticatedSocket.emit('room_left', { roomId, userId: user.userId });

      // Notify other players in the room
      authenticatedSocket.to(roomId).emit('player_left', { userId: user.userId });
    });

    // Handle player ready status
    authenticatedSocket.on('player_ready', (data) => {
      const { roomId, playerId, ready } = data;
      console.log(`Player ${playerId} ready status: ${ready} in room ${roomId}`);

      // Broadcast to room
      authenticatedSocket.to(roomId).emit('player_ready_update', { playerId, ready });
    });

    // Handle game actions
    authenticatedSocket.on('game_action', (data) => {
      const { roomId, playerId, action, gameData } = data;
      console.log(`Game action from ${playerId} in room ${roomId}:`, action);

      // Broadcast action to other players in the room
      authenticatedSocket.to(roomId).emit('opponent_action', {
        playerId,
        action,
        gameData,
        timestamp: Date.now()
      });
    });

    // Handle score submission
    authenticatedSocket.on('submit_score', (data) => {
      const { roomId, playerId, score, gameType } = data;
      console.log(`Score submission from ${playerId}: ${score} in room ${roomId}`);

      // TODO: Validate and submit score to Python backend

      // Broadcast score update to room
      authenticatedSocket.to(roomId).emit('score_update', {
        playerId,
        score,
        gameType,
        timestamp: Date.now()
      });
    });

    // Handle game start
    authenticatedSocket.on('start_game', (data) => {
      const { roomId, gameType } = data;
      console.log(`Starting game ${gameType} in room ${roomId}`);

      // Broadcast game start to all players in room
      io.to(roomId).emit('game_start', {
        gameType,
        startTime: Date.now(),
        message: 'Game has started!'
      });
    });

    // Handle game end
    authenticatedSocket.on('end_game', (data) => {
      const { roomId, gameType, results } = data;
      console.log(`Ending game ${gameType} in room ${roomId}`);

      // Broadcast game end to all players in room
      io.to(roomId).emit('game_end', {
        gameType,
        results,
        endTime: Date.now(),
        message: 'Game has ended!'
      });
    });

    // Handle disconnection
    authenticatedSocket.on('disconnect', () => {
      logger.info(`Authenticated client disconnected`, {
        socketId: authenticatedSocket.id,
        userId: user.userId,
        roomId: user.roomId
      });

      // Get all rooms this socket was in
      const rooms = Array.from(authenticatedSocket.rooms);

      // Clean up game states for each room (excluding the socket's own room)
      rooms.forEach(roomId => {
        if (roomId !== authenticatedSocket.id) {
          logger.info(`Cleaning up room due to disconnect`, {
            roomId,
            userId: user.userId
          });

          // Trigger cleanup in all game controllers
          fingerFrenzyController.cleanup(roomId);
          bingoController.cleanup(roomId);
          matchingMayhemController.cleanup(roomId);
          numberSequenceController.cleanup(roomId);

          // Notify other players in the room about disconnection
          authenticatedSocket.to(roomId).emit('player_disconnected', {
            userId: user.userId,
            socketId: authenticatedSocket.id,
            timestamp: Date.now()
          });
        }
      });

      // Clean up user session
      sessionService.removeUserSessionBySocket(authenticatedSocket.id);
    });

    // Basic message echo for testing
    authenticatedSocket.on('message', (data) => {
      console.log(`Received message: ${data}`);
      authenticatedSocket.emit('message', `Echo: ${data}`);
    });
  });
}
